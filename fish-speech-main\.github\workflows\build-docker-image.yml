name: Build Image

on:
  push:
    branches:
      - main
    tags:
      - "v*"

jobs:
  build:
    runs-on: ubuntu-latest-16c64g
    steps:
      - uses: actions/checkout@v4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Get Version
        run: |
          if [[ $GITHUB_REF == refs/tags/v* ]]; then
            version=$(basename ${GITHUB_REF})
          else
            version=nightly
          fi

          echo "version=${version}" >> $GITHUB_ENV
          echo "Current version: ${version}"

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PAT }}

      - name: Build and Push Image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: dockerfile
          platforms: linux/amd64
          push: true
          tags: |
            fishaudio/fish-speech:${{ env.version }}
            fishaudio/fish-speech:latest
          outputs: type=image,oci-mediatypes=true,compression=zstd,compression-level=3,force-compression=true
          cache-from: type=registry,ref=fishaudio/fish-speech:latest
          cache-to: type=inline

      - name: Build and Push Dev Image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: dockerfile.dev
          platforms: linux/amd64
          push: true
          build-args: |
            VERSION=${{ env.version }}
            BASE_IMAGE=fishaudio/fish-speech:${{ env.version }}
          tags: |
            fishaudio/fish-speech:${{ env.version }}-dev
            fishaudio/fish-speech:latest-dev
          outputs: type=image,oci-mediatypes=true,compression=zstd,compression-level=3,force-compression=true
          cache-from: type=registry,ref=fishaudio/fish-speech:latest-dev
          cache-to: type=inline

      - name: Push README to Dockerhub
        uses: peter-evans/dockerhub-description@v4
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PAT }}
          repository: fishaudio/fish-speech
