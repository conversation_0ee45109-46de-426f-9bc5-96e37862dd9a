name: "🕷️ Bug report"
description: |
  Please follow this template carefully to ensure we can address your issue quickly.
  Make sure to provide as much detail as possible, including logs and screenshots.
labels:
  - bug
body:
  - type: checkboxes
    attributes:
      label: Self Checks
      description: "To ensure timely help, please confirm the following:"
      options:
        - label: This template is only for bug reports. For questions, please visit [Discussions](https://github.com/fishaudio/fish-speech/discussions).
          required: true
        - label: I have thoroughly reviewed the project documentation (installation, training, inference) but couldn't find information to solve my problem. [English](https://speech.fish.audio/) [中文](https://speech.fish.audio/zh/) [日本語](https://speech.fish.audio/ja/) [Portuguese (Brazil)](https://speech.fish.audio/pt/)
          required: true
        - label: I have searched for existing issues, including closed ones. [Search issues](https://github.com/fishaudio/fish-speech/issues)
          required: true
        - label: I confirm that I am using English to submit this report (我已阅读并同意 [Language Policy](https://github.com/fishaudio/fish-speech/issues/515)).
          required: true
        - label: "[FOR CHINESE USERS] 请务必使用英文提交 Issue，否则会被关闭。谢谢！:）"
          required: true
        - label: "Please do not modify this template and fill in all required fields."
          required: true
  - type: dropdown
    attributes:
      label: Cloud or Self Hosted
      multiple: true
      options:
        - Cloud
        - Self Hosted (Docker)
        - Self Hosted (Source)
    validations:
      required: true
  - type: textarea
    attributes:
      label: Environment Details
      description: "Provide details such as OS, Python version, and any relevant software or dependencies."
      placeholder: e.g., macOS 13.5, Python 3.10, torch==2.4.1, Gradio 4.44.0
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps to Reproduce
      description: |
        Include detailed steps, screenshots, and logs. Use the correct markdown syntax for code blocks.
      placeholder: |
        1. Run the command `python -m tools.api_client -t "xxxxx"`
        2. Observe the console output error: `ModuleNotFoundError: No module named 'pyaudio'` (with screenshots or logs will be better)
    validations:
      required: true
  - type: textarea
    attributes:
      label: ✔️ Expected Behavior
      placeholder: Describe what you expected to happen.
    validations:
      required: false
  - type: textarea
    attributes:
      label: ❌ Actual Behavior
      placeholder: Describe what actually happened.
    validations:
      required: false
